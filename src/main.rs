use backtest::{config::Confi<PERSON><PERSON><PERSON><PERSON>, BacktestFramework, Result};
use tokio::signal;
use tracing::info;

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志 - 默认级别为info，输出到stdout
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::INFO)
        .with_writer(std::io::stdout)
        .init();

    info!("Starting backtest application");

    // 获取配置
    let config = ConfigManager::get()?;
    info!("Configuration loaded: exchange={:?}", config.exchange);

    // 创建并初始化框架
    let mut framework = BacktestFramework::new().await?;

    // 初始化基础组件（HTTP和WebSocket服务器）
    framework.initialize_components().await?;

    // 初始化数据处理管道（可选，如果有数据文件的话）
    if std::path::Path::new("./data").exists() {
        info!("Data directory found, initializing data processing pipeline");
        framework.initialize_data_pipeline().await?;
    } else {
        info!("No data directory found, skipping data processing pipeline");
    }

    // 启动所有组件
    framework.start().await?;

    info!("Backtest framework started successfully!");
    info!("Services available:");
    info!("  - HTTP API: http://127.0.0.1:{}", config.http_port);
    info!("  - WebSocket: ws://127.0.0.1:{}", config.websocket_port);
    info!(
        "  - Health check: http://127.0.0.1:{}/api/v1/health",
        config.http_port
    );

    // 等待Ctrl+C信号
    info!("Press Ctrl+C to shutdown");
    signal::ctrl_c().await.expect("Failed to listen for ctrl_c");

    info!("Shutdown signal received, stopping framework...");
    framework.shutdown().await?;

    info!("Backtest application stopped successfully");

    Ok(())
}
