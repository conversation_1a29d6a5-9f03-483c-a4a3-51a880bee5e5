use backtest::{cli::InteractiveCli, config::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BacktestFramework, Result};
use std::sync::Arc;
use tokio::signal;
use tracing::info;

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志 - 默认级别为info，输出到stdout
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::INFO)
        .with_writer(std::io::stdout)
        .init();

    info!("Starting backtest application");

    // 获取配置
    let config = ConfigManager::get()?;
    info!("Configuration loaded: exchange={:?}", config.exchange);

    // 创建并初始化框架
    let mut framework = BacktestFramework::new().await?;

    // 初始化基础组件（HTTP和WebSocket服务器）
    framework.initialize_components().await?;

    // 初始化数据处理管道（可选，如果有数据文件的话）
    if std::path::Path::new("./data").exists() {
        info!("Data directory found, initializing data processing pipeline");
        framework.initialize_data_pipeline().await?;
    } else {
        info!("No data directory found, skipping data processing pipeline");
    }

    // 启动所有组件
    framework.start().await?;

    info!("Backtest framework started successfully!");
    info!("Services available:");
    info!("  - HTTP API: http://127.0.0.1:{}", config.http_port);
    info!("  - WebSocket: ws://127.0.0.1:{}", config.websocket_port);
    info!(
        "  - Health check: http://127.0.0.1:{}/api/v1/health",
        config.http_port
    );

    // 创建框架的Arc包装以便在CLI中共享
    let framework_arc = Arc::new(framework);

    // 启动交互式CLI
    info!("Starting interactive CLI...");
    match InteractiveCli::new(framework_arc.clone()) {
        Ok(mut cli) => {
            // 在单独的任务中运行CLI，这样可以同时处理Ctrl+C信号
            let cli_handle = tokio::spawn(async move {
                if let Err(e) = cli.run().await {
                    eprintln!("CLI error: {}", e);
                }
            });

            // 等待Ctrl+C信号或CLI退出
            tokio::select! {
                _ = signal::ctrl_c() => {
                    info!("Shutdown signal received, stopping framework...");
                }
                _ = cli_handle => {
                    info!("CLI exited, stopping framework...");
                }
            }
        }
        Err(e) => {
            eprintln!("Failed to create interactive CLI: {}", e);
            info!("Falling back to signal-based shutdown");
            info!("Press Ctrl+C to shutdown");
            signal::ctrl_c().await.expect("Failed to listen for ctrl_c");
        }
    }

    // 关闭框架
    info!("Shutting down framework...");
    // 由于framework在Arc中，我们无法调用需要可变引用的shutdown方法
    // 在实际应用中，可以通过信号通道通知各组件关闭
    // 这里我们简单地让程序退出，tokio运行时会清理资源

    info!("Backtest application stopped successfully");

    Ok(())
}
