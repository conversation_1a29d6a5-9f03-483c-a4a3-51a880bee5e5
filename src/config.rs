use crate::types::Exchange;
use crate::{BacktestError, Result};
use chrono::{DateTime, Utc};
use once_cell::sync::Lazy;
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use std::sync::{Arc, RwLock};

/// 全局配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    /// 交易所名称
    pub exchange: Exchange,

    /// 回测开始时间
    pub start_time: DateTime<Utc>,

    /// 回测结束时间
    pub end_time: DateTime<Utc>,

    /// 历史数据路径
    pub data_path: PathBuf,

    /// WebSocket服务器端口
    pub websocket_port: u16,

    /// HTTP服务器端口
    pub http_port: u16,

    /// 日志级别
    pub log_level: String,

    /// 性能预期（微秒）
    pub performance_target_us: u64,
}

impl Default for Config {
    fn default() -> Self {
        // 设置一个较大的时间范围以包含测试数据
        let start_time = DateTime::from_timestamp(1751887400, 0).unwrap_or_else(|| Utc::now()); // 2025-07-07 11:23:20 UTC
        let end_time = DateTime::from_timestamp(1751887500, 0).unwrap_or_else(|| Utc::now()); // 2025-07-07 11:25:00 UTC

        Self {
            exchange: Exchange::Binance,
            start_time,
            end_time,
            data_path: PathBuf::from("./data"),
            websocket_port: 8080,
            http_port: 8081,
            log_level: "info".to_string(),
            performance_target_us: 500,
        }
    }
}

/// 线程安全的全局配置单例
static GLOBAL_CONFIG: Lazy<Arc<RwLock<Config>>> =
    Lazy::new(|| Arc::new(RwLock::new(Config::default())));

/// 配置管理器
pub struct ConfigManager;

impl ConfigManager {
    /// 获取配置的只读引用
    pub fn get() -> Result<Config> {
        GLOBAL_CONFIG
            .read()
            .map_err(|e| BacktestError::Config(format!("Failed to read config: {}", e)))
            .map(|config| config.clone())
    }

    /// 更新配置
    pub fn update<F>(updater: F) -> Result<()>
    where
        F: FnOnce(&mut Config),
    {
        let mut config = GLOBAL_CONFIG
            .write()
            .map_err(|e| BacktestError::Config(format!("Failed to write config: {}", e)))?;

        updater(&mut config);
        Ok(())
    }

    /// 从文件加载配置
    pub fn load_from_file(path: &PathBuf) -> Result<()> {
        let content = std::fs::read_to_string(path)
            .map_err(|e| BacktestError::Config(format!("Failed to read config file: {}", e)))?;

        let config: Config = serde_json::from_str(&content)
            .map_err(|e| BacktestError::Config(format!("Failed to parse config: {}", e)))?;

        Self::set(config)
    }

    /// 保存配置到文件
    pub fn save_to_file(path: &PathBuf) -> Result<()> {
        let config = Self::get()?;
        let content = serde_json::to_string_pretty(&config)
            .map_err(|e| BacktestError::Config(format!("Failed to serialize config: {}", e)))?;

        std::fs::write(path, content)
            .map_err(|e| BacktestError::Config(format!("Failed to write config file: {}", e)))?;

        Ok(())
    }

    /// 设置整个配置
    pub fn set(config: Config) -> Result<()> {
        let mut global_config = GLOBAL_CONFIG
            .write()
            .map_err(|e| BacktestError::Config(format!("Failed to write config: {}", e)))?;

        *global_config = config;
        Ok(())
    }

    /// 验证配置的有效性
    pub fn validate() -> Result<()> {
        let config = Self::get()?;

        if config.start_time >= config.end_time {
            return Err(BacktestError::Config(
                "Start time must be before end time".to_string(),
            ));
        }

        if !config.data_path.exists() {
            return Err(BacktestError::Config(format!(
                "Data path does not exist: {:?}",
                config.data_path
            )));
        }

        if config.websocket_port == config.http_port {
            return Err(BacktestError::Config(
                "WebSocket and HTTP ports must be different".to_string(),
            ));
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;

    #[test]
    fn test_config_manager() {
        // 测试默认配置
        let config = ConfigManager::get().unwrap();
        assert_eq!(config.websocket_port, 8080);

        // 测试更新配置
        ConfigManager::update(|config| {
            config.websocket_port = 9090;
        })
        .unwrap();

        let updated_config = ConfigManager::get().unwrap();
        assert_eq!(updated_config.websocket_port, 9090);
    }
}
